.section-blog-search {
    margin: -2rem 0;
    padding: 2rem 1.5rem;

    .blog-filters {
        width: 100%;
    }

    .blog-keyword {
        width: 100%;
    }

    .input-group .btn {
        border-radius: 1rem;

        // XS MOBILE
        @include media-breakpoint-down(sm) {
            padding-right: .75rem;
            padding-left: .75rem;
            font-size: .75rem
        }
    }
}

.blog-listing {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: center;
    align-items: stretch;

    // MOBILE
    @include media-breakpoint-down(lg) {
        flex-direction: column;
    }

    // DESKTOP
    @include media-breakpoint-up(lg) {
        flex-direction: row;
    }

    .blog-item {

        // MOBILE
        @include media-breakpoint-down(lg) {
            max-width: 360px;
            margin-right: auto;
            margin-left: auto;
        }

        // DESKTOP
        @include media-breakpoint-up(lg) {
            width: calc((100% - 4rem) / 3);
        }
    }

    .blog-item-link {
        display: block;
        overflow: hidden;
        height: 100%;
        color: $body-color;
        text-decoration: none;
        background-color: $white;
        border-radius: $border-radius;
        box-shadow: $box-shadow-sm;

        &:hover,
        &:focus,
        &:focus-within {
            color: $primary-700;
            background-color: $tertiary-100;
        }

        .blog-item-image {

            &:before {
                padding-top: calc((2 / 3) * 100%);
            }

            img {
                width: 100%;
                object-fit: cover;
            }
        }

        .blog-item-detail {
            padding: 2rem 1.5rem;
        }
    }
}

.blog-featured {
    display: flex;
    justify-content: flex-start;
    align-items: stretch;
    overflow: hidden;
    border-radius: $border-radius;
    box-shadow: $box-shadow-sm;

    // MOBILE
    @include media-breakpoint-down(lg) {
        flex-direction: column;
        max-width: 360px;
        margin-right: auto;
        margin-left: auto;
    }

    // DESKTOP
    @include media-breakpoint-up(lg) {
        flex-direction: row;
    }

    .blog-featured-primary {
        background-color: $tertiary-100;

        // MOBILE
        @include media-breakpoint-down(lg) {
        }

        // DESKTOP
        @include media-breakpoint-up(lg) {
            width: 60%;
        }

        .blog-item,
        .blog-item-link {
            height: 100%;
        }

        .blog-item-link {
            background-color: $primary-100;

            &:hover,
            &:focus,
            &:focus-within {
                color: $primary-700;
                background-color: $tertiary-100;
            }

            .blog-item-image {

                &:before {
                    padding-top: calc((2 / 3) * 100%);
                }

                img {
                    width: 100%;
                    object-fit: cover;
                }
            }

            .blog-item-detail {
                padding: 2rem 1.5rem;
            }
        }
    }

    .blog-featured-secondary {

        // MOBILE
        @include media-breakpoint-down(lg) {
        }

        // DESKTOP
        @include media-breakpoint-up(lg) {
            width: 40%;

            .blog-item {
                height: 25%;

                .blog-item-link,
                .blog-item-detail {
                    height: 100%;
                }
            }
        }

        .blog-item {

            &:not(:first-child) {
                border-top: solid 1px $tertiary-600;
            }
        }
    }

    .blog-item-link {
        display: block;
        overflow: hidden;
        height: 100%;
        color: $body-color;
        text-decoration: none;
        background-color: $white;

        &:hover,
        &:focus,
        &:focus-within {
            color: $primary-700;
            background-color: $tertiary-100;
        }

        .blog-item-image {

            &:before {
                padding-top: calc((2 / 3) * 100%);
            }

            img {
                width: 100%;
                object-fit: cover;
            }
        }

        .blog-item-detail {
            padding: 2rem 1.5rem;
        }
    }
}

.blog-detail {

    .blog-detail-image {

        // MOBILE
        @include media-breakpoint-down(lg) {
            margin-bottom: 2rem;
        }

        // DESKTOP
        @include media-breakpoint-up(lg) {
            margin-bottom: 4rem;
        }

        img {
            width: 100%;
        }
    }

    .blog-detail-content {
        margin-top: 2rem;

        img {
            max-width: 100%;
        }
    }
}

