<?php

// ILHM-0905 2021-10-22 JB - move these functions from clhms_application.inc.php
// so they could be used in other scripts


function getDaRowInsertIfNecessary($obj, $property_type, $manager_email){
    //ILHM-0734 [bb] check undefined before insert for manager_email
    if($manager_email=='undefined'){
        $manager_email='';
    }
    $applicationSql ="SELECT * FROM C_designation_application WHERE Members_item_id='{$obj->memberRow['item_id']}' AND property_type='$property_type' AND item_active=1"; //ILHM-1118
    $application_row = $obj->getOneRowSQL($applicationSql);
    if(!$application_row) {
        $insertApplicationSql = "INSERT INTO C_designation_application
                                                SET Members_item_id='{$obj->memberRow['item_id']}'
                                                ,is_generated_from_C_qualifying_properties='Y'
                                                , property_type='$property_type'
                                                , Training_Type='{$obj->memberRow['Training_Type']}'
                                                ,member_id ='{$obj->memberRow['member_id']}'
                                                ,first_name ='" . $obj->real_escape_string(stripslashes($obj->memberRow['first_name'])) . "'
                                                ,last_name ='" . $obj->real_escape_string(stripslashes($obj->memberRow['last_name'])) . "'
                                                ,office ='" . $obj->real_escape_string(stripslashes($obj->memberRow['office'])) . "'
                                                ,Franchise ='" . $obj->real_escape_string(stripslashes($obj->memberRow['Franchise'])) . "'
                                                ,manager_email ='" . $obj->real_escape_string(stripslashes($manager_email)) . "'
                        " . $obj->usual_fields('C_designation_application');
        $insertResult = $obj->query($insertApplicationSql);
        $applicationRowItem_id = $obj->insert_id();
        $notesA =date('F j, Y, g:ia') . "First Transaction submitted by {$obj->db_user_row['item_name']}  \n";
        updateC_designation_applicationNotesAndStatus($obj, $notesA, $applicationRowItem_id, 'Incomplete',0,$obj->memberRow['last_name'].', '. $obj->memberRow['first_name'],'' ,'!$application_row' ); //ILHM-0804

        $application_row=$obj->getOneRowV2('C_designation_application','item_id',$applicationRowItem_id);
    } else {
        if ($manager_email !=$application_row['manager_email']) {
            $sql = "UPDATE C_designation_application SET manager_email ='" . $obj->real_escape_string(stripslashes($manager_email)) . "' WHERE item_id = '{$application_row['item_id']}'";
            $obj->query($sql);
            // 2021-07-07 06:53:08AM bb need to update to match what we just saved
            $application_row['manager_email']=$manager_email;
        }
    }

    return $application_row;
}

function get_num_required_properties($property_type){
    if($property_type == 'real'){
        //ILHM-1364 B 2024-04-18 10:38:22  bb
        $num_required_properties=6;
    }elseif($property_type == 'guild' || $property_type == 'elite'){
        $num_required_properties=2;
    }else{//$property_type='clhms' or lps or advanced or adv_elite;//ILHM-0905 2021-09-23 14:39:36PM documented advanced
        $num_required_properties=3;
    }
    return $num_required_properties;
}

/**
 * @param $obj
 * @param $progressPercent_rounded
 * @param $designation
 * @param $property_type
 * @return string
 */
function get_progress_text($obj,$progressPercent_rounded,$designation,$property_type){
    //ILHM-1434 2024-08-16 05:15:48  bb
//    if($progressPercent_rounded == 100 && designationBusinessRule($obj,'FAC') &&  $obj->memberRow['onboard_designation']=='FAE'  ){
//        $progress_text = $obj->getFieldContents('Designation','item_name','FAEC','home_html_approved') ;
//        return '<br />
//                              <div class="alert alert-success" id="hundred_progress">'.$progress_text.'</div>';
//    }
    //ILHM-1227 2023-05-01 12:49:58   bb
    if(designationBusinessRule($obj,'can_buy_elite') ){
        //if approved, always show this
        $progress_text = $obj->getFieldContents('Designation','item_name','G','home_html_approved') ;
        return '<br />
                              <div class="alert alert-success" id="hundred_progress">'.$progress_text.'</div>';
    }
    //ILHM-1024 2022-02-10 05:33:14AM  bb refactored to use copy from Designations list.
    $pended =get_pended($obj, $property_type);
    $da_status = get_application_status($obj, $property_type);
    //bb 2023-03-01 14:20:34  bb ILHM-1118  ILHM-1210 2023-04-11 14:41:54  bb also return on Incomplete
    if($da_status == '' OR $da_status=='Incomplete'){
        return '';
    }
    //ILHM-1024 2022-02-10 04:36:11AM   bb
    //ILHM-1434 2024-08-16 05:43:04  bb
    if( $obj->memberRow['onboard_designation']=='FAE'  ) {
        $designation_row = $obj->getOneRowV2('Designation', 'item_name', str_replace('FA', 'FAE', $obj->memberRow['designation']));
    }else {
        $designation_row = $obj->getOneRowV2('Designation', 'item_name', $designation);
    }
    if($pended>0 ){
        //ILHM-1364 2024-05-31 15:16:22  don't show for real
        //ILHM-1419 2024-06-28 08:08:19  bb reversed, do show for real  # && $progress_text !='real'
        $progress_text = '<br />
                              <div class="alert alert-danger" id="hundred_progress">
                                <h2  style="text-align: center;">There is an issue with one or more properties on this application.</h2>
                              </div>';

//        return '<br />
//                              <div class="alert alert-danger" id="hundred_progress">'. $designation_row['home_html_pended']. '</div>';
        //ILHM-1024 cannot use designation_row content because it links back to this page
        return $progress_text;
    }
    $progress_text = '';
    switch ($progressPercent_rounded) {
        case 0:
            //$progress_text = "<br />Let's get started!";//ILHM-0905 2021-10-27 11:00:25AM bbRyan doesn't want to show the thermometer if 0
            $progress_text = '';//ILHM-0905 2021-10-28 17:06:37PM  don't show
            break;
        case 33:
            //$progress_text = '<br />One down, two to go! You’re doing great.';
            $progress_text = '';//ILHM-0905 2021-10-28 17:06:37PM  don't show
            break;
        case 50:
            //$progress_text = '<br />One down, one to go! You’re doing great.';
            $progress_text = '';//ILHM-0905 2021-10-28 17:06:37PM  don't show
            break;
        case 67:
            //$progress_text = '<br />Only one more to go. You can do it!';
            $progress_text = '';//ILHM-0905 2021-10-28 17:06:37PM  don't show
            break;
        case 100:
            //ILHM-0484.2 [Fx] 2020-10-12
//            if($designation=='FAC'){
//
//                $progress_text = '<br />
//                              <div class="alert alert-success" id="hundred_progress">
//                                <h2  style="text-align: center;">Your application has been conditionally approved and is now in review. <BR />
//                                <a href="/real-estate-agents/Cart_Set_Plus.html?promo_code='. getAdvPathPromoForMember($obj,$obj->memberRow) .'">Pay now to UNLOCK your training</a>.</h2>
//                              </div>';
//            }elseif($designation=='FAP'){
//                $progress_text = '<br />
//                              <div class="alert alert-success" id="hundred_progress">
//                                <h2  style="text-align: center;">Your application has been conditionally approved.<BR />
//                                Please allow up to two business days for final approval.  If more information is required, you will be contacted by a member of our team.</h2>
//                              </div>';
//            }else {
//                $progress_text = '<br />
//                              <div class="alert alert-success" id="hundred_progress">
//                                <h2  style="text-align: center;">Thank you for completing your application.<BR />
//                                Please allow up to one week for application review.</h2>
//                              </div>';
//            }
            //dd($da_status,$designation_row);
            if($da_status == 'Approved'){
                $progress_text = $designation_row['home_html_approved'];
            }else{//completed
//                if($da_status == 'Completed' && $property_type=='elite'){
//                    //ILHM-1118d 2022-12-01 07:15:38  bb
//                    // dd($da_status . $property_type);
//                    $progress_text = $obj->elite_application_complete_message;//  elite_application_complete_message
//                    //$progress_text = dd($obj->elite_application_complete_message);//  elite_application_complete_message
//                }else{
                //dd($designation_row);
                    $progress_text = $designation_row['home_html'];
//                }

            }

            //ILHM-1138 2022-10-20 06:17:46   bb
            if($da_status == 'Revised'){
                $progress_text= "<p>Thank you for revising your application.<BR />
                                Please allow up to 10 days for application review.</p>";
            }
            //ILHM-1024 2022-02-22 14:15:36PM  changed completing to submitting bb
            if($progress_text==''){// could be true if they haven't populated the respective fields
                $progress_text= "<p>Thank you for submitting your application.<BR />
                                Please allow up to 10 days for application review.</p>";
            }
            // {active_course_link}
            $progress_text = str_replace('{active_course_link}', '', $progress_text);
            //ILHM-1024 2022-04-13 03:55:07AM     bb added the following as we have a token Destination.home_html_approved
            $progress_text = str_replace('{promo_code}', getAdvPathPromoForMember($obj, $obj->memberRow), $progress_text);




            $progress_text = '<br />
                              <div class="alert alert-success" id="hundred_progress">'.$progress_text.'</div>';
            break;
    }


    $obj->log_message("progress_text $progress_text");
    return $progress_text;
}

function generateOneCertificationApplicationCard($obj,$i,$Member_item_id,$property_type,$file_path,$application_row=array(), $C_qualifying_properties_row =array(), $is_cbgl_registered = false, $enableTransactionForm = false ){
    //ILHM-1118 2023-03-09 15:19:00  bb DON'T check values if $completed

    if (empty($application_row)) {
        $applicationSql ="SELECT * FROM C_designation_application WHERE Members_item_id='{$obj->memberRow['item_id']}' AND property_type='$property_type' AND item_active=1"; //ILHM-1118
        $application_row = $obj->getOneRowSQL($applicationSql);
    }
    //$obj->log_message('FX DEBUG $application_row: '.print_r($application_row,true));
    // 2020-09-02 05:00:51AM bb
    // we want to always display 3 boxes
    // if the user has never submitted, there will not be any rows for this user and type in C_qualifying_properties
    // otherwise, there should be no more than 3, populate what we find

    //ILHM-1022 [FX] 2022-03-02
    $disabled =  $enableTransactionForm ? '' : ' disabled ' ;

    //ILHM-0630 [FX] 2021-03-02 Add country to CLHMS /GUILD application tool
    // can not use tablePop because the client want the US and Canada be the first two options
    $country = '
        <select class="custom-select country transaction_' . $i . '" id="country_'.$i.'" '.$disabled.'>
            <option value="">Select a Country</option>
    ';
    //ILHM-0679 [FX] 2021-04-6 new features for country
    $countries = array("United States" => "United States", "Canada"=>"Canada", "Other"=>"Other");
    foreach ($countries as $key => $option_text) {
        $selected = $C_qualifying_properties_row['country'] == $key ? ' selected' : '';
        $country .= '
            <option value="'.$key.'" '.$selected.'>'.$option_text.'</option>
        ';
    }

    $country .= '
        </select>
    ';

    if($C_qualifying_properties_row['is_affirmation']=='Y'){
        $checked=' checked="checked" ';
    }else{
        $checked='';
    }
    $closing_date='';
    if($C_qualifying_properties_row['closing_date']){
        $closing_date=date('m/d/Y', strtotime(normalizeDate($C_qualifying_properties_row['closing_date']) )) ;

    }
    $check_html ='&nbsp;<i class="fa fa-check" aria-label="Submitted" aria-hidden="true" style="color:#56B68C"></i>';
    if( ($C_qualifying_properties_row['pended_reason']!='' || $C_qualifying_properties_row['is_pended']=='Y')  ){
        //ILHM-1364 G 2024-05-31 15:18:29  bb don't show real
        //ILHM-1419 2024-06-28 08:05:56  bb reversed, do show for real && $property_type !='real'
        //ILHM-0938 [FX] 2021-10-13 fixed if multiple pended reason is visible
        //ilhm-1138 2022-10-12 05:23:48   bb changed text to "Action Required"
        $pended_reason_html ='<div id="pended_content_' . $i . '" class="alert alert-danger pended_content"><strong>Action Required</strong><br />
                              '. $C_qualifying_properties_row['pended_reason'] .'
                                </div>
                        <div class="form-group">
                            <label for="comments" >Reply with comments or question:</label>
                            <textarea class="form-control" id="comments_' . $i . '" name="comments" ></textarea>
                        </div>';
        //$check_html ='&nbsp;<i class="fa fa-xmark" aria-label="Not Approved" aria-hidden="true" style="color:#fad8d8"></i>';
    }else{
        $pended_reason_html ='';
    }

    $obj->log_message("$check_html");
    if($C_qualifying_properties_row['is_approved']=='Y'){
        $approved_html ='<div id="status_alert_' . $i . '" class="alert alert-success">Approved</div>';
    }elseif($C_qualifying_properties_row['revised_date']!='0000-00-00' && $C_qualifying_properties_row['revised_date']!=''   ){//
        $approved_html ='<div id="status_alert_' . $i . '"  class="alert alert-success">Revised</div>';
    }elseif($C_qualifying_properties_row['item_id']>0 && $C_qualifying_properties_row['is_pended']=='N'){
        $approved_html ='<div id="status_alert_' . $i . '"  class="alert alert-success">Submitted</div>';

    }else{
        $approved_html ='<div id="status_alert_' . $i . '"  ></div>';
            $check_html='';

    }

    // ILHM-1364 [JJ] 2024-045-01 switching from columns to vertical stack for the cards to accommodate more than 3 form cards
    // also making the html a little easier to follow

    // Add class .disabled to .card for forms not yet available
    $skelHTML = '
    <div class="card '. $disabled .' mb-5">
        <div class="card-body">
            <h2 class="card-title" id="card_title_' .$i. '">
                <!-- <span class="fas fa-home-alt text-peach d-block mb-3" aria-hidden="true"></span> -->
                Qualified Transaction ' . $i . $check_html.'
            </h2>'.$approved_html.'
            <!-- <p class="small"><a href="#heading1">Do my transactions qualify?</a></p> -->
            <input type="hidden" name="is_cbgl_registered"  class="is_cbgl_registered"  value="' . $is_cbgl_registered . '">
            <form method="post" id="form_' . $i . '">
                <input type="hidden" name="item_id" id="item_id_' . $i . '" value="' . $C_qualifying_properties_row['item_id'] . '">
                <input type="hidden" name="Member_item_id"  id="Member_item_id_' . $i . '"  value="' . $Member_item_id . '">
                <input type="hidden" name="member_id"  id="member_id_' . $i . '"  value="' . $obj->memberRow['member_id'] . '">
                <input type="hidden" name="propertyNo" class="propertyNo" id="propertyNo_' . $i . '"  value="' . $i . '">
                <input type="hidden" name="property_type"  id="property_type_' . $i . '"  value="' . $property_type . '">
                <input type="hidden" name="is_pended"  id="is_pended_' . $i . '"  value="' . $C_qualifying_properties_row['is_pended'] . '">' . $pended_reason_html .'
                <input type="hidden" name="is_ZIP"  id="is_ZIP_' . $i . '"  value="' . $C_qualifying_properties_row['is_ZIP']  . '">
                <div class="row">
                    <div class="col-12 col-md-6">
    ';

    //address
    $skelHTML .= '
                        <div class="form-group">
                            <label for="address_' . $i . '">Address</label>
                            <input class="form-control address transaction_' . $i . '" type="text" id="address_' . $i . '" name="address" value="' . $C_qualifying_properties_row['address'] . '"'.$disabled.'>
                            <span style="display:none;color:red" class="address_alert alert_' . $i . '" id="address_alert_' . $i . '">Please enter the address.</span>
                        </div>
    ';

    //ILHM-0630 [FX] 2021-03-02 Add country to CLHMS /GUILD application tool
    $skelHTML .= '
                        <div class="form-group">
                            <label for="country_' . $i . '">Country</label>
                            ' . $country . '
                            <span style="display: none; color: red;" class="country_alert alert_' . $i . '" id="country_alert_' . $i . '">Please select a Country.</span>
                        </div>
    ';

    $skelHTML .= get_state_html($obj, $i, $C_qualifying_properties_row['country'],$C_qualifying_properties_row['state'], $C_qualifying_properties_row['state'], $is_cbgl_registered,'initial_load', $enableTransactionForm);//, $is_cbgl_registered
    $skelHTML .= get_county_html_V2($obj, $i, $is_cbgl_registered,$C_qualifying_properties_row, $C_qualifying_properties_row, 'initial_load', $enableTransactionForm);
    $skelHTML .= get_zip_html($obj, $i, $is_cbgl_registered, $C_qualifying_properties_row, $C_qualifying_properties_row, 'initial_load', $enableTransactionForm);

    // change columns
    $skelHTML .= '
                    </div>
                    <div class="col-12 col-md-6">
    ';

    //Closing Date HTML
    //ILHM-0938 [FX] 2021-10-13 add closing date check when initial loading
    $check_closing_date    = check_closing_date($obj, $C_qualifying_properties_row['closing_date'], $is_cbgl_registered, $property_type, $C_qualifying_properties_row['property_no'], $C_qualifying_properties_row['request_from'],$C_qualifying_properties_row['item_id']);
    $show_date_alert = ( ($check_closing_date['show_date_alert'] == 'Y' ) ? 'display:block;' : 'display:none;');
    $skelHTML .= '
                        <div class="form-group">
                            <label for="closing_date_' . $i . '">Closed Date</label>
                            <input class="form-control closing_date transaction_' . $i . '" type="text"  placeholder="MM/DD/YYYY" id="closing_date_' . $i . '" name="closing_date" value="' . $closing_date . '" '.$disabled.'>
                            <p style="color:#ff0000;'.$show_date_alert.'" class="closing_date_alert alert_' . $i . '" id="closing_date_alert_' . $i . '">'.$check_closing_date['message'].'
                            </p>
                            <p style="display:none;color:#ff0000" class="closing_date_validate_alert alert_' . $i . '" id="closing_date_validate_alert' . $i . '">The closed date is invalid, please double check.
                            </p>
                        </div>
    ';

    //sales price
    $county_threshold = get_county_threshold($obj, $C_qualifying_properties_row, $property_type );
    $zip_threshold = get_zip_threshold($obj, $C_qualifying_properties_row,$property_type,$C_qualifying_properties_row,'generateOneCertificationApplicationCard');//ILHM-0906 [FX 2021-09-27 add new param $C_qualifying_properties_row


    //ILHM-0673b 2021-04-26 06:27:38AM bb
    //$skelHTML .= get_price_HtmlV2($obj, $i, $property_type, $C_qualifying_properties_row);

    // ILHM-ILHM-0673b 2021-05-20 JB - refactor to use price feedback in back-end form
    $skelHTML .= getQualifyingPropertyPriceFeedbackHtml ($obj, $i, $property_type, true, $C_qualifying_properties_row, $enableTransactionForm);

    $skelHTML .= '    <!-- PRETTY VERSION, NEEDS JS TO FUNCTION -- https://getbootstrap.com/docs/4.5/components/forms/#file-browser
                        <div class="form-group">
                            <label for="attachment_filename">Supporting Documentation</label>
                            <div class="custom-file">
                                <input class="custom-file-input" type="file" id="attachment_filename" name="attachment_filename" '.$disabled.'>
                                <div class="custom-file-label">Choose file</div>
                            </div>
                        </div>
                        -->';
    $style_valid_show = ' style="display:none" ';


    $check_show_button_div = check_show_button_div($obj, $county_threshold,$zip_threshold['zip_threshold'], $check_closing_date, $C_qualifying_properties_row,$is_cbgl_registered, 'initial');
    $filesUploaded = fileUploadedForQualifyingProperty ($obj, $i, $C_qualifying_properties_row['item_id']);

    if($check_show_button_div == 'Y'){
        $style_valid_show = '';
    }

    if(!$filesUploaded || !$checked) {
        $submitDisabled = 'disabled="disabled"';
    }
    //ILHM-0766 [FX] 2021-07-02
    $obj->log_message('FX DEBUG $property_type: '.print_r($property_type,true));
    if ($obj->db_page_row['item_name'] == 'lps-application') {
        //ILHM-1022 [FX]  make the manager_email field on the LPS (only) application required
        $skelHTML .= '
                        <div class="form-group">
                            <label for="manager_email_' . $i . '">Manager Email</label>
                            <input class="form-control manager_email transaction_' . $i . '" type="text" id="manager_email_' . $i . '" name="manager_email" value="' . $application_row['manager_email'] . '" '.$disabled.' >
                            <span style="display:block;" class="hint manager_email_hint_' . $i . '" id="manager_email_hint_' . $i . '">Please provide a valid email address and your manager will be notified of your application and approval.</span>
                        </div>
        ';
    }

    //if($C_qualifying_properties_row['is_approved']!='Y') {
    if($C_qualifying_properties_row['item_id']>0){
        //always show if we do have a saved row
        $style_valid_show = 'style="display:block"';
    }else{
        //never show if we don't have a saved row
        $style_valid_show = 'style="display:none"';
    }
    $obj->log_message("style_html $style_valid_show");
    //ILHM-0905-- don't even show the following if approved
    //ILHM-1423 2024-07-10 06:39:44  bb don't show the affirmation checkbox anymore
    $skelHTML .= '
                       <div id="valid_showing_' . $i . '" ' . ' class="valid_showing_' . $i . ' ' . $county_threshold . ' ' . $check_show_button_div . '" '. $style_valid_show.'>
                            <div class="form-group">
                                <!--<div class="custom-control custom-checkbox">
                                    <input type="checkbox" name="is_affirmation" id="is_affirmation_' . $i . '" class="custom-control-input is_affirmation transaction_' . $i . '" value="" '.$disabled.'>
                                    <label class="custom-control-label" for="is_affirmation_' . $i . '" ' . $checked . ' id="label_is_affirmation_' . $i . '">I affirm that the details on this submission are accurate and have not been altered.</label>
                                    <input type="hidden" name="form_index" value="' . $i . '" />
                                    <input type="hidden" name="docs_uploaded" id="docs_uploaded_' . $i . '" value="' . (bool)$filesUploaded . '" />
                                </div>-->
                            </div>
                        </div>
                    </div> <!-- / column -->
                </div> <!-- /.row -->
            </form>
    ';
    $skelHTML .= '
            <div ' . $style_valid_show . ' class="valid_showing_' . $i . ' " >
                <label for="attachment_filename">Supporting Documentation</label>
                <!-- <p><a href="#heading2"><span style="font-size: x-small;">What are documentation requirements?</span></a></p> -->
                <div id="doc_filenames_' . $i . '">
                    ' . $filesUploaded . '
                </div>
                <form action="/qualifying-property-document-upload.html" class="dropzone" id="dz_area_' . $i . '" style="display: flex; justify-content: center; align-items: center;">
                    <div class="dz-message"><strong>Click Here to Upload Your Documentation</strong></div>
                    
                    <input type="hidden" name="item_id" value="' . $C_qualifying_properties_row['item_id'] . '" />
                    <input type="hidden" name="form_index" value="' . $i . '" />
                    <input type="hidden" name="member_id" value="' . $obj->memberRow['item_id'] . '" />
                    <input type="hidden" name="property_type" value="' . $property_type . '" />
                </form>
                <span><strong><em>Note: File names should not contain special characters and should be simple.</em></strong></span>
                <p>
                    <br />
                    <span style="display:none;color:green" class="submit_success_hint hint" id="submit_success_hint_' . $i . '">Submited successfully!</span>
                    <span style="display:none;color:green" class="update_success_hint hint" id="update_success_hint_' . $i . '">Updated successfully!</span>
                    <br /><br />
    ';


    if ($C_qualifying_properties_row['is_approved'] != 'Y') {
        //ILHM-0905 2021-10-28 11:07:13AM  bb change submit button to update if we have an item_id
        if ($C_qualifying_properties_row['item_id'] > 0) {
            $button_text = 'update ';
        } else {
            $button_text = 'submit';
        }
        $button_html ='<p><b>By submitting this form, I affirm that the details on this submission are accurate and have not been altered.</b></p>';
        $button_html .= '<button class="btn btn-green submit" id="submit_' . $i . '" ' . $submitDisabled . ' data-formindex="' . $i . '">' . $button_text.' Transaction '.$i . '</button>';
        //$button_html .= '</form>';
    } else {
        $button_html = '';
    }
    $skelHTML .='
                    '.$button_html.'
                </p>
            </div>
    ';

    //ILHM-0855 [FX] 2021-07-14
    $skelHTML .= $C_qualifying_properties_row['completed_date'] == '0000-00-00' || !$C_qualifying_properties_row['completed_date'] ? '<a href="" class="clear-form" id="clear_form_' . $i . '" ' . '   >Clear Form</a>' : '';
    $skelHTML .=  '
        </div> <!-- /.card-body -->
    </div> <!-- /.card -->
    ';

    $obj->db_page_row['insert_body'] .='
    <script type="text/javascript">
    Dropzone.options.dzArea'.$i.' = {
        maxFilesize: 50,
        acceptedFiles: "image/jpg, image/jpeg, image/png, image/gif, application/pdf",
        init: function() {
            this.on("success", function(file,response) {
                console.log(response);
                $("#doc_filenames_'.$i.'").append("<p style=\'color:green;\' id=\'documents_item_id_" +response.documentId+ "\'><em><a style=\'color:green;font-size:16px\' href=\'/qualifying_properties/" +response.filename+ "\' target=\'_blank\'>" +response.originalFilename+ "</a></em>  <a href=\'\' class=\'remove-file-x\' style=\'color:red;text-decoration: none;\' data-documents-item-id=\'" +response.documentId+ "\' id=\'remove_file_'.$i.'\'> X </a></p>");
                $("#docs_uploaded_'.$i.'").val(1);
                $("#dz_area_'.$i.'").children(".dz-message").html("<p><strong style=\'color:green;font-size:18px\'>File Uploaded</strong><br/>Click to select or drop another file.</p>");
                $("#submit_' .$i. '").removeAttr("disabled");
            });
        },
        complete: function (file) {
            this.removeFile(file);
        }
    };
    </script>
    ';

    return $skelHTML;
}


// ILHM-0735 2021-05-21 JB - new function to get html for uploaded documents
function fileUploadedForQualifyingProperty ($obj, $formIndex, $propertyId=0) {
    if(!$propertyId) {
        // GET BY SESSION ID AND FORM INDEX
        $result = $obj->query("SELECT item_id, attachment_filename, original_filename
        FROM C_qualifying_property_documents
        WHERE
            session_id='" .session_id(). "'
            AND form_index='" .(int)$formIndex. "'
            AND C_qualifying_properties_item_id <1
            AND Members_item_id='" .$obj->memberRow['item_id']. "'"
        );

    } else {
        // GET BY PROPERTY ID
        $result = $obj->query("SELECT item_id, attachment_filename, original_filename
        FROM C_qualifying_property_documents
        WHERE
            C_qualifying_properties_item_id='" .(int)$propertyId. "'
            AND Members_item_id='" .$obj->memberRow['item_id']. "'"
        );
    }

    if(!$result) {
        return false;
    }

    $response = '';
    while($row = $obj->fetch_assoc($result)) {
        //ILHM-0855 [FX] 2021-07-14
        $complete_data = $obj->getFieldContents('C_qualifying_properties', 'item_id',(int)$row['C_qualifying_properties_item_id'],'completed_date');
        $remove_file_x = '<a href="" class="remove-file-x" style="color:red;text-decoration: none;" data-documents-item-id="'.$row['item_id'].'" id="remove_file_'.$formIndex.'"> X </a>' ;
        $remove_file_x = $complete_data == '0000-00-00' || !$complete_data ? $remove_file_x : '';
        $response .= '<p style="color:green;" id="documents_item_id_'.$row['item_id'].'"><em><a style="color:green;font-size:16px" href="/qualifying_properties/' .$row['attachment_filename']. '" target="_blank" >' .$row['original_filename']. '</a></em> '.$remove_file_x.'</p>';
    }

    return $response;
}


function check_closing_date($obj, $closing_date_post, $is_cbgl_registered, $property_type, $property_no, $call_from='',$item_id): array {
    //ILHM-1118 2023-03-09 15:48:12  bb
    $return['show_date_alert'] = 'N';//not alert
    $return['message'] = '';
    $return['reason'] = '';
    $return['item_id'] = $item_id;
    if($item_id){
        $C_qualifying_properties_row = $obj->getOneRowV2('C_qualifying_properties','item_id',$item_id);

        $return['completed_date'] = $C_qualifying_properties_row['completed_date'];
        if($C_qualifying_properties_row && $C_qualifying_properties_row['completed_date'] != '0000-00-00' && $C_qualifying_properties_row['completed_date'] !=''){
            return $return;
        }
    }
    $obj->log_message(" \$closing_date_post $closing_date_post ");
    $obj->log_message(" \$is_cbgl_registered $is_cbgl_registered ");

    //   $return['action'] = 'N';//not alert

    //todo: ILHM-0905 fx why is this remarked off?  I believe it is true that we don't have a date restriction on guild. If we removed this here are we doing it elsewhere?
//    if($property_type=='guild'){
//        //No date restrictions on GUILD
//        return $return;
//    }
    $return['closing_date_post'] = $closing_date_post;
    if ($closing_date_post) {
        $post_closing_date = reformat_date('Y-m-d', $closing_date_post);
        //ILHM-0673b testing 2021-05-06 04:17:30AM bb don't allow dates in the future
        if($post_closing_date > date('Y-m-d')  ){
            $return['show_date_alert']='Y';
            $return['message']= "This closed date is in the future.";//'Qualified Properties must be within the last 24 months.';
            return $return;
        }
        //ILHM-0779 [BB, FX] 2021-06-07 Update to LPS/CLHMS Application - ANY 24 month period, means the LPS will have same rules as CLHMS for the closing date period
        //    if($is_cbgl_registered == "Y"){
        if(true){ // strtolower($property_type) !='elite'){ //ILHM-1153 now ALL applications must be within trailing 24 months ILHM-1118 bb all incl elite are 24months
            //ILHM-0673 2021-04-08 11:01:52AM bb
            //if $is_cbgl_registered:
            //   ALL dates must be within the last 24 months
            //ILHM-1153 per jill, want it to allow any date within the same month 2 years ago
            //if($post_closing_date < date('Y-m-d',strtotime('- 731 days',strtotime(date('Y-m-d'))))  ){
            $testDate = date('Y-m-d',strtotime(date( date('Y')-2 .'-' . date('m') .'-01'  ))) ;
            //$obj->log_message($testDate);
            $return['testDate']=$testDate;
            $return['post_closing_date']=$post_closing_date;
            if($post_closing_date < $testDate){
                $return['show_date_alert']='Y';

                $return['message']= "This closed date is not within the last 24-months.";//'Qualified Properties must be within the last 24 months.';
            }

        }
//        else {
//            // 1 yr for elite
//            $testDate = date('Y-m-d',strtotime(date( date('Y')-1 .'-' . date('m') .'-01'  ))) ;
//            //$obj->log_message($testDate);
//            if($post_closing_date < $testDate){
//                $return['show_date_alert']='Y';
//                $return['message']= "This closed date is not within the last 12-months.";//'Qualified Properties must be within the last 24 months.';
//            }
//        }
//          No longer used as of 2022-12-05 06:52:54  bb
            //            //ILHM-0825 [FX] 2021-07-08 explode the current closing date when do the checking
//            $sql_check = "SELECT MIN(closing_date) as min_date_except_empty FROM C_qualifying_properties WHERE closing_date > '0000-00-00' ".($property_no? " AND propertyNo != $property_no " :'')."  AND property_type='$property_type' AND Members_item_id='{$obj->memberRow['item_id']}'";
//            $minDate_row = $obj->getOneRowSQL($sql_check);
//            $min_date_except_empty = reformat_date('Y-m-d', $minDate_row['min_date_except_empty']);
//            //$obj->log_message($sql_check."$min_date_except_empty");
//            $minUnix = strtotime($min_date_except_empty.'00:00:00');
//            $sql_check = "SELECT MAX(closing_date) as max_date_except_empty FROM C_qualifying_properties WHERE closing_date > '0000-00-00' ".($property_no? " AND propertyNo != $property_no " :'')." AND property_type='$property_type' AND Members_item_id='{$obj->memberRow['item_id']}'";
//            $maxDate_row = $obj->getOneRowSQL($sql_check);
//            $max_date_except_empty = reformat_date('Y-m-d', $maxDate_row['max_date_except_empty']);
//            $maxUnix = strtotime($max_date_except_empty.'00:00:00');
//            //$obj->log_message("$max_date_except_empty");
//
//            $return['min_date_except_empty'] = $min_date_except_empty;
//            $return['max_date_except_empty'] = $max_date_except_empty;
//            $sql_rest = "SELECT * FROM C_qualifying_properties WHERE closing_date > '0000-00-00' ".($property_no? " AND propertyNo != $property_no " :'')." AND property_type='$property_type' AND Members_item_id='{$obj->memberRow['item_id']}'";
//            $count_rest_date_except_empty_and_current = $obj->countQuery($sql_rest);
//            $return['count_rest_date_except_empty_and_current_transaction_date'] = $count_rest_date_except_empty_and_current;
//            if ($count_rest_date_except_empty_and_current == 2) {
//                $maxMinDiffDays = round(abs($maxUnix -$minUnix)  / (60 * 60 * 24));
//                $return['maxMinDiffDays'] = $maxMinDiffDays;
//                if (abs($maxMinDiffDays) > 731) {
//                    $return['show_other_two_date_alerts'] = 'Y';
//                } else {
//                    $return['show_other_two_date_alerts'] = 'N';
//                }
//            }
//
//            //$test_date = $min_date_except_empty;
//            //$post_closing_date = reformat_date('Y-m-d', $_POST['closing_date']);
//            if ($minDate_row['min_date_except_empty']) {
//                $closingDateUnix= strtotime($post_closing_date.'00:00:00');
//                $maxDiff = abs($closingDateUnix -$maxUnix);
//                $minDiff = abs($closingDateUnix - $minUnix);
//
//                $diff_days = round(max($maxDiff,$minDiff) / (60 * 60 * 24));
//                //$obj->log_message("\$post_closing_date $post_closing_date\$min_date_except_empty $min_date_except_empty \$max_date_except_empty $max_date_except_empty \$diff_days $diff_days");
//
//                if (abs($diff_days) > 731) {//731 gives 1 day grace for leap year
//                    $return['show_date_alert'] = 'Y';//alert
//                    $return['message'] = 'This closing date is not within 24-months of one of your other saved properties. Please use a different property or clear your other property first.';
//                }
//            }
//            if ($post_closing_date && !$minDate_row['min_date_except_empty'] && $call_from =='initial_load') {
//                $return['show_date_alert'] = 'N';
//                $return['reason'] = 'No closing date existed.';
//            }
//        }
    }else {
        if ($call_from == 'zip' || $call_from == 'price' || $call_from == 'county'|| $call_from == 'country' || $call_from == 'address') {
            $return['show_date_alert'] = 'Y';//alert
        }
        $return['message'] = 'Please enter a date.';
        $return['reason'] = 'Closed date is empty.';
    }
    //$obj->log_message('FX DEBUG $return: '.print_r($return,true));
    return $return;
}

function check_zip_valid($obj,$zip): bool {
    // what if a zip isn't in the db but IS a valid zip: 0906 said return 500,000
    $sql = 'SELECT * FROM C_counties WHERE item_name="' . trim($zip.'"');
    $zip_check_count = $obj->countQuery($sql);
    //ILHM-0906 [FX] 2021-09-21 show $500.000 threshold for any 5-digit zip not found in the db
    // In this case, the param $zip has been checked in clhms_application.js which is 5 digits number
    //So we always return true here to trade the $zip is valid. For more, see get_zip_threshold()
    //ILHM-0906 [FX] 2021-09-22 reverse back -new zip with Threshold w default file
    return $zip_check_count > 0;
    // return true;
}

function check_show_button_div($obj, $threshold, $zip_threshold,$check_closing_date, $all_values,$is_cbgl_registered, $call_from=''): string
{
    $check_province_string = $all_values['check_province_string'];
    $check_province_array = explode(',', $check_province_string);
    //ILHM-0746 [FX] 2021-05-24
    if ($call_from == 'initial') {
        $all_values['price'] = $all_values['sale_price'];
    }

//    if ($all_values['price'] >= $threshold && $check_closing_date['show_date_alert'] === "N" && (($all_values['state'] && $all_values['county']) ||  $all_values['zip'] || in_array($all_values['state'], $check_province_array) || $all_values['country'] == 'Other') ) {
//        return 'Y';
//    }else {
//        return 'N';
//    }
    // 2021-04-26 11:12:47AM bb simplified the above which was about to get more complex and fragile
    //$obj->log_message("N0. check_show_button_div \$call_from $call_from \$threshold $threshold \$zip_threshold $zip_threshold  ".print_r($all_values,true));

    if( ($all_values['state'] && $all_values['county']) ||  $all_values['zip'] || in_array($all_values['state'], $check_province_array) || $all_values['country'] == 'Other') {
        //$good=true;
    }else {
        //$obj->log_message("N1 check_show_button_div \$call_from $call_from \$threshold $threshold \$zip_threshold $zip_threshold  ".print_r($all_values,true));
        return 'N';
    }
    //ILHM-0938 [fx] check and show button div except check closing date, the closing date will be checked in front end in this case.
    if ( $call_from != 'check_show_button_div_except_closing_date') {
        if($check_closing_date['show_date_alert'] !== "N"){
            //$obj->log_message("N2 check_show_button_div \$call_from $call_from \$threshold $threshold \$zip_threshold $zip_threshold  ".print_r($all_values,true));
            return 'N';
        }
    }

    if($all_values['property_type']=='guild' && $all_values['price'] >=1000000){
        return 'Y';
    }elseif($all_values['property_type']=='guild' && $all_values['price'] < 1000000){
        //$obj->log_message("N3 check_show_button_div \$call_from $call_from \$threshold $threshold \$zip_threshold ".print_r($zip_threshold,true)." \$is_cbgl_registered $is_cbgl_registered  \$all_values: ".print_r($all_values,true));

        return 'N';
    }

    if($is_cbgl_registered=='Y' && $all_values['price'] >= $zip_threshold  ){
        //$obj->log_message("check_show_button_div \$threshold $threshold \$zip_threshold $zip_threshold  ".print_r($all_values,true));
        return 'Y';
    }else{
        if($is_cbgl_registered!='Y' && $all_values['price'] >= $threshold  ){
            //$obj->log_message("check_show_button_div \$threshold $threshold \$zip_threshold $zip_threshold  ".print_r($all_values,true));
            return 'Y';
        }else{
            //$obj->log_message("N4 check_show_button_div \$call_from $call_from \$threshold $threshold \$zip_threshold ".print_r($zip_threshold,true)." \$is_cbgl_registered $is_cbgl_registered  \$all_values: ".print_r($all_values,true));
            return'N';
        }
    }

}

function check_selected_state_in_given_country($obj, $state, $country): string {
    $states_row = $obj->getOneRowV2('States', 'item_name', $state);
    $result = 'N/A';
    if ($states_row['country']) {
        $result = $states_row['country'] == $country ? 'Y' : 'N';
    }

    return $result;
}

function get_counties_by_selected_states($obj, $selected_state, $C_qualifying_properties_item_id): array {
    $sql = 'SELECT *
            FROM C_counties
            WHERE item_active = 1
            AND state = "'.$selected_state.'"';

    $result = $obj->query($sql);
    $county_arr = [];
    while($row = $obj->fetch_assoc($result)) {
        $county_arr[$row['item_id']] = $row['item_name'];
    }
    $selected_county = $C_qualifying_properties_item_id ? $obj->getFieldContents("C_qualifying_properties", "item_id", (int)$C_qualifying_properties_item_id, 'county') : '';

    return array( 'counties' => $county_arr, 'selected_county' => $selected_county);
}

/**
 * @param $obj
 * @param $i
 * @param $selected_country
 * @param $state_saved
 * @param $selected_state
 * @param $is_cbgl_registered
 * @param string $call_from
 * @return string
 */
function get_state_html($obj, $i, $selected_country, $state_saved, $selected_state, $is_cbgl_registered, $call_from='', $enableTransactionForm = true): string
{
    $condition = '';
    $select_a = '';

    if ($selected_country == "United States" ) {
        $condition = ' AND country="US" ';
        $select_a  = 'Select a State';
    } else if ($selected_country == "Canada") {
        $condition = ' AND country="CA" AND item_name !="__"';
        $select_a  = 'Select a Province';
    } else  { //($selected_country == "Other" || !$selected_country )

    }
    $obj->log_message("$selected_country");
    $show_state_alert = 'display:none;';
    $state_or_province = '';
    if ($selected_country == "United States"  ||  $selected_country == "Canada" ) {
        $state_display = ' ';
        if ($call_from == 'country') {            //saved state in the selected country
            $country_by_selected_country = 'Other';
            if ($selected_country == "United States" ) {
                $country_by_selected_country = 'US';
                $state_or_province = 'state';
            } else if ($selected_country == "Canada" ) {
                $country_by_selected_country = 'CA';
                $state_or_province = 'province';
            }
            $country_by_saved_state =  $obj->getFieldContents("States", "item_name", $state_saved, 'country');
            if ($country_by_saved_state == $country_by_selected_country) {
                $show_state_alert = 'display:none;';
            } else {
                $show_state_alert = 'display:block;';
            }
            if (!$selected_state && $state_saved) {
                $show_state_alert = 'display:block;';
            }
        }
    } else {
        $state_display = ' style="display:none;" ';
    }
    //ILHM-0905 2021-10-22 10:19:20AM bb
    $additional_class ='custom-select state transaction_' . $i ;
    if($call_from=='lookup-tool'){
        $additional_class .=' form-control styled-select js-example-responsive';
    }
    //ILHM-1022 [FX] 2022-03-02
    $disabled =  $enableTransactionForm ? '' : ' disabled ' ;//'.$disabled.'
    $state = tablePop('state',$state_saved,'States',//$C_qualifying_properties_row['state']
        array(
            'TP_display_field'		=>'full_name',
            'TP_value_field'		=>'item_name',
            'TP_where'				=>'WHERE item_active=1 '. $condition . ' ', // use 'WHERE 1' to see them all
            'TP_order_by'			=>'list_sort',
            'TP_select_attr'		=>'id="state_'.$i.'"  class="'.$additional_class .'" '.$disabled.'',
            'TP_addOption1'			=>array($select_a,'',false),
            'TP_addOption3'			=>array('Other Territory','OT',true)
        ));
    $html = '
                        <div class="form-group" '.$state_display.'>
                            <label for="state_' . $i . '">State/Prov</label>
    ';

    $html .= $state;
    $html .= '
                            <span style="'.$show_state_alert.'color:red" class="state_alert alert_' . $i . '" id="state_alert_' . $i . '">Please select a '.$state_or_province.'.</span>
                        </div>
    ';
    return $html;
}

function check_selected_county_in_selected_state($obj, $selected_county, $selected_state): string {
    $return = '';
    $C_counties_row = $obj->getOneRowV2('C_counties', 'item_name', $selected_county);

    if ($selected_state == $C_counties_row['state']) {
        $return = 'Y';
    } else {
        $return = 'N';
    }
    return $return;

}

/**
 * @param $obj
 * @param $i
 * @param $is_cbgl_registered
 * @param $selected_values
 * @param $current_property_row
 * @param $call_from
 * @return string
 */
function get_county_html_V2($obj, $i, $is_cbgl_registered, $selected_values, $current_property_row, $call_from, $enableTransactionForm = true): string {

    $selected_country = $selected_values['country'];
    $selected_state   = $selected_values['state'];
    $selected_county  = $selected_values['county'];

    //$obj->log_message("selected_values" . print_r($selected_values, true));

    $show_county_alert = 'display:none;';
    $show_county       = 'display:block;';
    $show_county_div   = '';
    //$obj->log_message('FX DEBUG $selected_country: '.print_r($selected_country,true));
    if ($selected_country == "Other" || $selected_country == "") {
        $show_county_div = ' style="display: none;" ';
    } else {

    }
    $HTML = '
                        <div class="form-group" '.$show_county_div.'>
    ';
    //ILHM-0905 2021-10-22 10:19:20AM bb
    $additional_class='custom-select county transaction_' . $i ;
    if($call_from=='lookup-tool'){
        $additional_class.=' form-control styled-select js-example-responsive';
        //$additional_class ='form-control styled-select js-example-responsive select2-hidden-accessible';
    }
    //ILHM-1022 [FX] 2022-03-02
    $disabled =  $enableTransactionForm ? '' : ' disabled ' ;//'.$disabled.'
    $county_html = '<select name="county" id="county_'.$i.'" class="'.$additional_class.'" style="'.$show_county.'" '.$disabled.'>';
    $first_option =   '<option value="">Select a ' .(($selected_country == 'United States')? 'County' : 'Market Area'). '</option>';
    $NA_option =   '<option value="N/A" selected="selected">N/A</option>';
    $is_selected_county = false;
    //   $test_0746 = '0';
    if ($call_from == 'initial_load' || $call_from == 'country') {
        if ($current_property_row['country'] == "United States" || $current_property_row['country'] == "Canada") {
            if ($current_property_row['state'] && $current_property_row['county']) {// && state in the selected country
                $country_by_selected_country = 'Other';
                if ($selected_values['country'] == "United States" ) {

                    $country_by_selected_country = 'US';
                } else if ($selected_values['country'] == "Canada" ) {

                    $country_by_selected_country = 'CA';
                }

                $country_by_saved_state = $obj->getFieldContents("States", "item_name", $current_property_row['state'], 'country');
                if ($country_by_saved_state == $country_by_selected_country ) {//saved state in the selected country
                    $helper_results     = get_county_options_html_V2_helper($obj, $first_option, $selected_state, $current_property_row['county'], $is_selected_county,$call_from,$current_property_row['state']);
                    $is_selected_county = $helper_results['is_selected_county'];
                    $county_html       .= $helper_results['county_html'];

                } else {
                    $first_option =   $NA_option;
                    $county_html .= $first_option;
                    $show_county_alert = 'display:block;';

                }
            }

        } else if ($current_property_row['country'] != '') {
            $first_option =   $NA_option;
            $county_html .= $first_option;
            $show_county_alert = 'display:block;';

        } else {
            if ($call_from == 'country') {
                $show_county_alert = 'display:block;';
            }else {
                $show_county_alert = 'display:none;';
            }
            $first_option =   $NA_option;
            $county_html .= $first_option;

        }
    } else if ($call_from == 'state' || $call_from == 'lookup-tool') { // state_on_change
        $helper_results     = get_county_options_html_V2_helper($obj, $first_option, $selected_state, $current_property_row['county'], $is_selected_county,$call_from,$current_property_row['state']);
        $is_selected_county = $helper_results['is_selected_county'];
        $county_html       .= $helper_results['county_html'];

    }

    if (($is_selected_county || ($selected_country == "United States" && $is_cbgl_registered == "Y")) && $call_from != 'country'){
        $show_county_alert = 'display:none;';
        if ((!$selected_county ||  $selected_county = 'N/A') && $call_from != 'initial_load') {
            $show_county_alert = ' display: block; ';
        }
    } else {
        $show_county_alert = 'display:block;';
    }


    $county_html .= '</select>';
    //ILHM-0746 [FX] 2021-05-26
    if(strpos($county_html, $NA_option) !== false){
        $show_county_alert = 'display:none;';
    }


    if($call_from=='lookup-tool'){
        $show_county_alert = 'display:none;';
    }

    $HTML .= '
                            <label for="county_' . $i . '"  id="county_label_' . $i . '">County/Market Area</label>
                            '.$county_html. '
                            <span style="'.$show_county_alert.'color:red" class="county_alert alert_' . $i . '" id="county_alert_' . $i . '">Please select a county.</span>
                        </div>
    ';


    return $HTML;
}

function get_county_options_html_V2_helper($obj, $first_option, $selected_state, $saved_county, $is_selected_county,$call_from, $saved_state): array {
    $state_condition = $selected_state;
    if ($call_from == 'country') {
        $state_condition = $saved_state;
    }

    $county_html = $first_option;
    $county_sql = 'SELECT * FROM C_counties WHERE item_active=1 AND state = "'.$state_condition.'"';//'.$state_condition;
    $result = $obj->query($county_sql);
    //ILHM-0746 [FX] 2021-05-26
    $has_results = "N";
    while($row = $obj->fetch_assoc($result)) {
        $has_results = "Y";
        $selected = '';
        if ($saved_county == $row['item_name']) {
            $selected = ' selected ';
            $is_selected_county = true;
        }
        $county_html .= '<option value="' . $row['item_name'] . '" ' . $selected . '>' . $row['item_name'] . '</option>';
    }
    //ILHM-0746 [FX] 2021-05-26
    if ($has_results == "N") {
        $county_html = '<option value="N/A" selected="selected">N/A</option>';

    }

    return array('county_html' => $county_html, 'is_selected_county' => $is_selected_county);
}

function get_zip_html($obj, $i, $is_cbgl_registered, $selected_values, $current_property_row, $call_from, $enableTransactionForm = true): string {

    $selected_country = $selected_values['country'];
    $selected_zip     = $selected_values['zip'];
    $saved_zip        = $current_property_row['zip'];
    $show_zip_div     = 'display: none;';
    $zip_value        = '';
    $show_zip_alert   = ' display:none; ';
    if ($selected_country == "United States" && $is_cbgl_registered == "Y") {
        $show_zip_div = 'display: block;';
        $zip_value = $selected_zip ? $selected_zip : $saved_zip;
    }  else {
        $show_zip_alert = ' display:none; ';
    }//else if  (($selected_country == "United States" && $is_cbgl_registered != "Y") ||  $selected_country == "Canada" ) {} else {}


    if ($call_from != 'initial_load' && $is_cbgl_registered == 'Y' && $selected_values['country'] == 'United States' && !$selected_values['zip']) {
        $show_zip_alert = ' display:block; ';
    } else {
        $show_zip_alert = ' display:none; ';
    }
    //ILHM-1022 [FX] 2022-03-02
    $disabled =  $enableTransactionForm ? '' : ' disabled ' ;//'.$disabled.'
    $html = '
                        <div class="form-group" style="'.$show_zip_div.'">
                            <label for="zip_' . $i . '"  id="zip_label_' . $i . '">Zip Code</label>
                            <input type="text" name="zip" id="zip_'.$i.'" class="form-control zip zip-code transaction_' . $i . '"  value="' . $zip_value . '" '.$disabled.'>
                            <span style="'.$show_zip_alert.'color:red" class="zip_alert alert_' . $i . '" id="zip_alert_' . $i . '">Please enter the zip code.</span>
                        </div>
    ';
    //   $html .= '<span>$show_zip_div:'.$show_zip_div.'</span>';
    return $html;
}

function getQualifyingProperties($obj,$property_type,$is_cbgl_registered){
    //ILHM-0905 2021-09-23 14:53:03PM bb reviewed for advanced-application
    //ILHM-1364 2024-05-16 11:40:08  bb as of 2024-05-16 11:40:11  $is_cblg_registered is no longer considered. We manage this strictly by property_type
    $obj->log_message("\$is_cbgl_registered $is_cbgl_registered  ");

    if($property_type == 'guild'){
        $sqlPart ="  AND p.sale_price >= 1000000
    ";
    }elseif($property_type == 'lps'){
        $sqlPart =" AND p.sale_price >= p.threshold_at_submit_ZIP AND p.threshold_at_submit_ZIP >0";
    }else{
    //clhms  or advanced
        $sqlPart =" AND p.sale_price >= p.threshold_at_submit AND p.threshold_at_submit >0";
    }
    $sql_progress ="SELECT  COUNT(p.item_id) AS count_ids FROM C_qualifying_properties p
                WHERE 1
                $sqlPart
                AND p.property_type='$property_type'
                AND p.Members_item_id='{$obj->memberRow['item_id']}'
                #AND p.attachment_filename!=''    we are no longer storing in attachment_filename 2021-05-21 15:40:39PM
                AND p.address != ''
                AND item_active=1"; //ILHM-1118
    $obj->log_message($sql_progress);
    $progress_row = $obj->getOneRowSQL($sql_progress);
    return$progress_row['count_ids'] ;
}

function get_pended($obj,$property_type) {
    //ILHM-0905 2021-09-23 14:53:03PM bb reviewed for advanced-application
    $sql_pended = "SELECT  p.item_id AS count_ids FROM C_qualifying_properties p
                WHERE 1
                
                AND p.property_type='$property_type'
                AND p.Members_item_id='{$obj->memberRow['item_id']}'
                AND pended_date !='0000-00-00'
                 AND item_active=1"; //ILHM-1118
    //$obj->log_message($sql_pended);
    $pended = $obj->countQuery($sql_pended);
    return $pended;
}

function get_application_status($obj,$property_type){
    $sql = "SELECT  da_status FROM C_designation_application
                WHERE 1
                
                AND property_type='$property_type'
                AND Members_item_id='{$obj->memberRow['item_id']}'
                AND item_active=1"; //ILHM-1118

    $app_row = $obj->getOneRowSQL($sql);
    return $app_row['da_status'];
}


function linkQualifyingPropertyDocuments($obj, $formIndex, $sessionId, $property_type, $propertyId, $applicationRowItem_id) {
    // ILHM-0735 2021-05-21 JB - new function to link uploaded documents with property_id

    // 2021-05-23 12:59:42PM bb updated to add property_type and C_qualifying_properties_item_id=0
    //ILHM-0905 2021-09-23 14:53:03PM bb reviewed for advanced-application
    $sql = "UPDATE C_qualifying_property_documents SET
        C_qualifying_properties_item_id='" .(int)$propertyId. "'
        WHERE
        form_index='" .(int)$formIndex. "'
        AND session_id='" .$obj->real_escape_string($sessionId). "'
        AND property_type='$property_type'
        AND Members_item_id='" .$obj->memberRow['item_id']. "'
        AND C_qualifying_properties_item_id=0
    ";
    $result = $obj->query($sql);

    if(!$result) {
        $obj->log_message("ERROR linking documents to property", "E");
        return false;
    }

    // ILHM-0746 2021-05-26 JB - refactor appending notes of C_designation_application into
    // separate function in cust_functions.inc.php so we can all it elsewhere
    appendDesignationApplicationNotesWithDocuments($obj, $propertyId, $applicationRowItem_id);

    return true;
}

//ILHM-0765 [FX] 2021-07-19 add popup modal. [FX] 2021-08-20 undo, the client canceled this functionality
//function check_lps_modal($obj, $property_type, $data_arr, $call_from) {
//    //ILHM-0905 2021-09-23 14:56:02PM reviewed for advanced-application but it's unclear this is called
//    $result['show_modal'] = false;
//    $clhms_sql   = 'SELECT item_id FROM C_designation_application  WHERE is_clhms = "Y" AND member_id = '.(int)$obj->memberRow['member_id'];
//    $count_clhms = $obj->countQuery($clhms_sql);
//    $lps_sql     = 'SELECT item_id FROM C_designation_application WHERE is_lps = "Y" AND member_id = '.(int)$obj->memberRow['member_id'];
//    $count_lps   = $obj->countQuery($lps_sql);
//    $result['count_clhms'] = $count_clhms;
//    $result['count_lps']   = $count_lps;
//    $result['property_type']   = $property_type;
//
//    if ($property_type == 'lps' && $count_clhms == 3 && $count_lps == 2) {
//        //there are two lps, both of them need to be qualify for clhms and lps
//        //, and the checking one needs to be qualify for clhms and not qualify for lps, popup the modal window.
//        $clhms_threshold   = get_county_threshold($obj, $data_arr, $property_type );
//        $zip_threshold_arr = get_zip_threshold($obj, $data_arr,$property_type);
//        $lps_threshold     = $zip_threshold_arr['zip_threshold'];
//        $new_sales_price   = (int)$data_arr['price'];
//
//        $result['clhms_threshold'] = $clhms_threshold;
//        $result['lps_threshold']   = $lps_threshold;
//        $sql = 'SELECT * FROM `C_qualifying_properties`
//                WHERE member_id  =  '.(int)$obj->memberRow['member_id'].'
//                AND property_type = "lps" ';
//        $res = $obj->query($sql);
//        $check_two_lps_qualify_clhms_and_lps_threshold = false;
//        while ($row = $obj->fetchAssoc($res)) {
//            if ($row['sale_price'] >= $clhms_threshold && $row['sale_price'] >= $lps_threshold) {
//                $check_two_lps_qualify_clhms_and_lps_threshold = true;
//            } else {
//                $check_two_lps_qualify_clhms_and_lps_threshold = false;
//                break;
//            }
//        }
//
//        if ($clhms_threshold && $lps_threshold && $check_two_lps_qualify_clhms_and_lps_threshold && $new_sales_price >= $clhms_threshold && $new_sales_price < $lps_threshold) {
//            $result['show_modal'] = true;
//        }
////        $result['show_modal'] = true;
//
//    }
//
//    return $result;
//}

/**
 * @param $obj
 * @param $item_id
 * @param $new_property_type
 * @return array
 */
function switch_application($obj, $item_id, $new_property_type): array
{
    $new_property_type = strtolower($new_property_type);
    $old_property_type = $obj->getFieldContents('C_designation_application', 'item_id', (int)$item_id, 'property_type');
    $message = '';
    $count = 0;
    try {
        if ($new_property_type != $old_property_type) {
            $update_sql_1  = "UPDATE C_designation_application SET
                                property_type = '$new_property_type'
                              , notes         = CONCAT(notes, '" . date('F j, Y, g:ia ') . " Updated property_type FROM $old_property_type TO $new_property_type. \n')
                              WHERE item_id   = '" . (int)$item_id. "'";
            $result = $obj->query($update_sql_1);
            if (!$result) {
                $message .= "Error on updating C_designation_application, SQL: <br/>".$update_sql_1."<br />";
                throw new Exception($message,__LINE__);
            }else{
                $message .=" Updated Application ID $item_id to $new_property_type.<br/>";
            }
            $count++;
            $sql = 'SELECT * FROM C_qualifying_properties WHERE C_designation_application_item_id = "'.(int)$item_id.'"';
            $res = $obj->query($sql);
            while ($row = $obj->fetchAssoc($res)) {
                $clear_sql = '';
                if ( $new_property_type == 'guild') {
                    $clear_sql = ' , threshold_at_submit_ZIP = ""
                           , threshold_at_submit     = "" ';
                } else if ( $row['property_type'] == 'lps') {
                   // 2022-05-18 10:12:13AM    bb we don't want to clear this one $clear_sql = ' , threshold_at_submit_ZIP = "" ';
                }
                //CONCAT(notes,'" . date('F j, Y, g:ia ') . $_POST['notes'] . "\n')
                $update_sql_2  = "UPDATE C_qualifying_properties SET
                                   property_type = '$new_property_type'
                                  , notes        = CONCAT(notes, '" . date('F j, Y, g:ia ') . " Updated property_type FROM ".$row['property_type']." TO $new_property_type, \n')
                                   $clear_sql
                                 WHERE item_id   = '" . (int)$row['item_id']. "'";
                $result_2 = $obj->query($update_sql_2);

                if (!$result_2) {
                    $message .= "Error on updating C_qualifying_properties, SQL:".$update_sql_2;
                    throw new Exception($message,__LINE__);
                }else{
                    $message .=" Updated Transaction ID {$row['item_id']} to $new_property_type.<br/>";
                }
                $count++;
            }
        } else {
            $message = 'The new_property_type is same as current property type ('.$new_property_type.').';
        }
    } catch(Exception $e) {
        $obj->log_message("Error code: " . $e->getCode() . ' ' . $e->getMessage(),'E');
        return array('message' => $message, 'result' => false);
    }
    return array('message' => $message.'Updated rows: '.$count, 'result' => true );

}


// this function called by list_C_qualifying_properties_edit_v2.
function get_qualifying_properties_filter_html($obj) {
    //ILHM-1241 2023-07-13 10:44:41  merged FAC and FAP because during the FA path Designation can change. We should NOT be filtering by designation in this tool.
    // removed items like  AND Members.designation='FAP' from the queries
    if($_GET['reset']=='true'){
        unset($_SESSION['pre_filter']);
        unset($_SESSION['qualifying_properties']);
        header('Location: /a/editC_qualifying_properties.html');
        exit();
    }
    $html = '';

    //-----
    $html .= '<!-- filters start -->
    <div class="pre-filter-groups" style="margin-bottom: 15px;">
        <div class="row">
            <div class="col-md-3">';
    $html .= '<form id="filter_preID" method="post" action="/a/KDpg_item_quick_chg.html" class="form-inline">
                    <input type="hidden" name="list_id" value="pre_filter">
                    <select name="filter_pre" id="filter_pre" onchange="$(\'#filter_preID\').submit();" class="form-control">';
    //$options = array("Choose Filter", "ADVANCED FAP", "ADVANCED FAC", "CLHMS", "LPS", "GUILD", "GUILD Elite");
    //$options = array("Choose Filter",  "ADVANCED", "CLHMS", "LPS", "GUILD", "GUILD Elite","Real Luxury","Advanced ELITE");
    //ILHM-1488 2024-09-13 09:01:56  re-arranged per request
    $options = array("Choose Filter","Advanced ELITE",  "ADVANCED", "GUILD Elite","Real Luxury", "LPS", "CLHMS", "GUILD");
    foreach ($options as $key => $val) {
        $selected = ($key == $_SESSION['pre_filter']['filter_pre']? 'selected' : '');
        $html .=  '<option value="'.$key.'" '.$selected.'>'.$val.'</option>';
    }
    $html .= '      </select>
			    </form>
            </div>
            <div class="col-md-2">
                <a href="/a/editC_qualifying_properties.html?reset=true">Reset&nbsp;Filters</a>
            </div>
        </div>
    </div>
<div class="filter-groups">';

    if($_SESSION['pre_filter']['filter_pre']){
        //level selector
        switch ($_SESSION['pre_filter']['filter_pre']) {
            //ILHM-1488 2024-09-17 15:30:39  bb re-ordered cases to match new order of $options
            case '1':
                //show Advanced Elite
                $html .= '
                     <div class="row">
                         <div class="col-md-4">';
                //NEW Real Luxury
                $html .= $obj->listFilter('qualifying_properties', '28', 'C_qualifying_properties', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', member_id,')')", 'NEW Ready for Advanced Elite Luxury Review', " is_approved !='Y' AND revised_date='0000-00-00' AND completed_date !='0000-00-00' AND is_pended='N'  AND property_type='adv_elite' AND item_active=1", 'Members_item_id HAVING COUNT(Members_item_id)>=3 ', '', '', '', '/a/editC_qualifying_properties.html') ;
                $html .= '</div>
                          <div class="col-md-4">';
                //Pending Real Luxury
                $html .= $obj->listFilter('qualifying_properties', '29', 'C_qualifying_properties', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', member_id,')')", 'Pending Advanced Elite Luxury', " approved_date='0000-00-00' AND is_pended='Y'   AND property_type='adv_elite' AND item_active=1", 'Members_item_id ', '', '', '', '/a/editC_qualifying_properties.html') ;
                $html .= '</div>
                          <div class="col-md-4">';
                //Real Luxury
                $html .= $obj->listFilter('qualifying_properties', '30', 'C_designation_application', 'item_id', "CONCAT(completed_date,' ',last_name,', ',first_name,' (', member_id,')')", 'Revised Ready for Advanced Elite Luxury Review V2', "  da_status='Revised' AND property_type='adv_elite' AND item_active=1", 'Members_item_id  ', '', '', '', '/a/editC_qualifying_properties.html');
                $html .= '</div>
                     </div>';
                break;
            case '2':
                //show ADVANCED FAC
                $html .= '
                    <div class="row">
                        <div class="col-md-4">';
                //NEW ADVANCED FAC
                $html .= $obj->listFilter('qualifying_properties', '16', 'C_qualifying_properties LEFT JOIN Members ON Members.item_id=Members_item_id', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', Members.designation,')')", 'NEW Ready for ADVANCED Review'
                    , " approved_date='0000-00-00' AND Members.designation !='C' AND Members.designation !='G' AND  Members.designation !='GE' AND revised_date='0000-00-00' AND completed_date !='0000-00-00'  AND  is_pended='N' AND property_type='advanced' AND C_qualifying_properties.item_active=1 ", 'Members_item_id HAVING COUNT(Members_item_id)>=3 ', '', '', '', '/a/editC_qualifying_properties.html') ;
                $html .= '</div>
                          <div class="col-md-4">';
                //Pending ADVANCED FAC
                $html .= $obj->listFilter('qualifying_properties', '17', 'C_qualifying_properties LEFT JOIN Members ON Members.item_id=Members_item_id', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', Members.designation,')')", 'Pending ADVANCED'
                    , " approved_date='0000-00-00' AND Members.designation !='C' AND Members.designation !='G' AND  Members.designation !='GE' AND is_pended='Y' AND property_type='advanced' AND C_qualifying_properties.item_active=1", 'Members_item_id ', '', '', '', '/a/editC_qualifying_properties.html') ;
        
                $html .= '</div>
                          <div class="col-md-4">';
                //revised ADVANCED V2 FAC
                $html .= $obj->listFilter('qualifying_properties', '19', 'C_designation_application LEFT JOIN Members ON Members.item_id=Members_item_id', 'C_designation_application.item_id', "CONCAT(completed_date,' ',C_designation_application.last_name,', ',C_designation_application.first_name,' (', Members.designation,')')", 'Revised Ready for ADVANCED Review'
                    , "  da_status='Revised' AND Members.designation !='C' AND Members.designation !='G' AND  Members.designation !='GE' AND  property_type='advanced' AND C_designation_application.item_active=1", 'Members_item_id  ', '', '', '', '/a/editC_qualifying_properties.html');
                $html .= '</div>
                         </div>';
                break;
    
            case '3':
                //show GUILD Elite
                $html .= '
                     <div class="row">
                         <div class="col-md-4">';
                //NEW ELITE
                $html .= $obj->listFilter('qualifying_properties', '21', 'C_qualifying_properties', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', member_id,')')", 'NEW Ready for GUILD Elite Review', " approved_date='0000-00-00' AND revised_date='0000-00-00' AND completed_date !='0000-00-00' AND is_pended='N'  AND property_type='elite' AND item_active=1", 'Members_item_id HAVING COUNT(Members_item_id)>=2 ', '', '', '', '/a/editC_qualifying_properties.html') ;
                $html .= '</div>
                          <div class="col-md-4">';
                //Pending ELITE
                $html .= $obj->listFilter('qualifying_properties', '22', 'C_qualifying_properties', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', member_id,')')", 'Pending GUILD Elite', " approved_date='0000-00-00' AND is_pended='Y'   AND property_type='elite' AND item_active=1", 'Members_item_id ', '', '', '', '/a/editC_qualifying_properties.html') ;
                $html .= '</div>
                          <div class="col-md-4">';
                //revised ELITE V2
                $html .= $obj->listFilter('qualifying_properties', '24', 'C_designation_application', 'item_id', "CONCAT(completed_date,' ',last_name,', ',first_name,' (', member_id,')')", 'Revised Ready for GUILD Elite Review V2', "  da_status='Revised' AND property_type='elite' AND item_active=1", 'Members_item_id  ', '', '', '', '/a/editC_qualifying_properties.html');
                $html .= '</div>
                     </div>';
                break;
    
            case '4':
                //show Real Luxury
                $html .= '
                     <div class="row">
                         <div class="col-md-4">';
                //NEW Real Luxury
                $html .= $obj->listFilter('qualifying_properties', '25', 'C_qualifying_properties', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', member_id,')')", 'NEW Ready for Real Luxury Review', " is_approved !='Y' AND revised_date='0000-00-00' AND completed_date !='0000-00-00' AND is_pended='N'  AND property_type='real' AND item_active=1", 'Members_item_id HAVING COUNT(Members_item_id)>=6 ', '', '', '', '/a/editC_qualifying_properties.html') ;
                $html .= '</div>
                          <div class="col-md-4">';
                //Pending Real Luxury
                $html .= $obj->listFilter('qualifying_properties', '26', 'C_qualifying_properties', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', member_id,')')", 'Pending Real Luxury', " approved_date='0000-00-00' AND is_pended='Y'   AND property_type='real' AND item_active=1", 'Members_item_id ', '', '', '', '/a/editC_qualifying_properties.html') ;
                $html .= '</div>
                          <div class="col-md-4">';
                //Real Luxury
                $html .= $obj->listFilter('qualifying_properties', '27', 'C_designation_application', 'item_id', "CONCAT(completed_date,' ',last_name,', ',first_name,' (', member_id,')')", 'Revised Ready for Real Luxury Review V2', "  da_status='Revised' AND property_type='real' AND item_active=1", 'Members_item_id  ', '', '', '', '/a/editC_qualifying_properties.html');
                $html .= '</div>
                     </div>';
                break;
    
            case '5':
                //show LPS
                $html .= '
                     <div class="row">
                         <div class="col-md-4">';
                //NEW LPS
                $html .= $obj->listFilter('qualifying_properties', '10', 'C_qualifying_properties', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', member_id,')')", 'NEW Ready for LPS Review', " approved_date='0000-00-00' AND revised_date='0000-00-00' AND completed_date !='0000-00-00' AND is_pended='N'  AND property_type='lps' AND item_active=1", 'Members_item_id HAVING COUNT(Members_item_id)>=3 ', '', '', '', '/a/editC_qualifying_properties.html') ;
                $html .= '</div>
                          <div class="col-md-4">';
                //Pending LPS
                $html .= $obj->listFilter('qualifying_properties', '11', 'C_qualifying_properties', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', member_id,')')", 'Pending LPS', " approved_date='0000-00-00' AND is_pended='Y'   AND property_type='lps' AND item_active=1", 'Members_item_id ', '', '', '', '/a/editC_qualifying_properties.html') ;
                $html .= '</div>
                          <div class="col-md-4">';
                //revised LPS V2
                $html .= $obj->listFilter('qualifying_properties', '14', 'C_designation_application', 'item_id', "CONCAT(completed_date,' ',last_name,', ',first_name,' (', member_id,')')", 'Revised Ready for LPS Review V2', "  da_status='Revised' AND property_type='lps' AND item_active=1", 'Members_item_id  ', '', '', '', '/a/editC_qualifying_properties.html');
                $html .= '</div>
                     </div>
                                  ';
                break;
                
            case '6':
                //show CLHMS
                $html .= '
                    <div class="row">
                         <div class="col-md-4">';
                //NEW CLHMS
                $html .= $obj->listFilter('qualifying_properties', '1', 'C_qualifying_properties', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', member_id,')')", 'NEW Ready for CLHMS Review', " approved_date='0000-00-00' AND revised_date='0000-00-00' AND completed_date !='0000-00-00' AND is_pended='N' AND property_type='clhms' AND item_active=1", 'Members_item_id HAVING COUNT(Members_item_id)>=3 ', '', '', '', '/a/editC_qualifying_properties.html') ;
                $html .= '</div>
                          <div class="col-md-4">';
                //Pending CLHMS
                $html .= $obj->listFilter('qualifying_properties', '6', 'C_qualifying_properties', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', member_id,')')", 'Pending CLHMS', " approved_date='0000-00-00' AND is_pended='Y' AND property_type='clhms' AND item_active=1", 'Members_item_id ', '', '', '', '/a/editC_qualifying_properties.html') ;

                $html .= '</div>
                          <div class="col-md-4">';
                //ILHM-0916 base this filter on C_designation_application
                //Revised CLHMS v2
                $html .= $obj->listFilter('qualifying_properties', '13', 'C_designation_application', 'item_id', "CONCAT(completed_date,' ',last_name,', ',first_name,' (', member_id,')')", 'Revised Ready for CLHMS Review V2', "  da_status='Revised' AND property_type='clhms' AND item_active=1", 'Members_item_id  ', '', '', '', '/a/editC_qualifying_properties.html');

                $html .= '</div>
                     </div>               ';
                break;

            case '7':
                //show GUILD
                $html .= '
                     <div class="row">
                        <div class="col-md-4">';
                //NEW GUILD
                $html .= $obj->listFilter('qualifying_properties', '2', 'C_qualifying_properties LEFT JOIN Members ON Members_item_id=Members.item_id', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', C_qualifying_properties.member_id,')')", 'NEW Ready for GUILD Review', " revised_date='0000-00-00' AND Members.designation='C' AND C_qualifying_properties.approved_date='0000-00-00'  AND completed_date !='0000-00-00' AND is_pended='N' AND property_type='guild' AND C_qualifying_properties.item_active=1", 'Members_item_id HAVING COUNT(Members_item_id)>=2 ', '', '', '', '/a/editC_qualifying_properties.html') ;
                $html .= '</div>
                          <div class="col-md-4">';
                //Pending GUILD
                $html .= $obj->listFilter('qualifying_properties', '8', 'C_qualifying_properties', 'Members_item_id', "CONCAT(completed_date,' ',member_name,' (', member_id,')')", 'Pending GUILD', " approved_date='0000-00-00' AND is_pended='Y' AND property_type='guild' AND item_active=1", 'Members_item_id ', '', '', '', '/a/editC_qualifying_properties.html') ;
                $html .= '</div>
                          <div class="col-md-4">';
                //revised GUILD V2
                $html .= $obj->listFilter('qualifying_properties', '15', 'C_designation_application', 'item_id', "CONCAT(completed_date,' ',last_name,', ',first_name,' (', member_id,')')", 'Revised Ready for GUILD Review V2', "  da_status='Revised' AND property_type='guild' AND item_active=1", 'Members_item_id  ', '', '', '', '/a/editC_qualifying_properties.html');
                $html .= '</div>
                     </div>';
                break;

            default:
                break;
        }
    }
    $html .= '
   </div>';
    $html .= '
    <div class="stable-filters" style="margin-left:15px;">
        <div class="row">
             <div class="col-md-4">
               <div class="row">
                    <label class="control-label">Last Name</label>
                    <form id="filter_4ID" method="post" action="/a/KDpg_item_quick_chg.html" >
                        <input type="hidden" name="list_id" value="qualifying_properties" />
                        <input  class="form-control" type="text" name="filter_4" id="last_nameID" onchange="document.getElementById(\'filter_4ID\').submit();" value="' . $_SESSION['qualifying_properties']['filter_4'] . '"/>
                        <input type="button" onclick="document.getElementById(\'last_nameID\').value=\'\';document.getElementById(\'filter_4ID\').submit();" class="btn  btn-default" value="Clear" style="vertical-align: top;"/>
                    </form>
                </div>
             </div>
             <div class="col-md-4">
                <label class="control-label">First Name</label>
                 <form id="filter_5ID" method="post" action="/a/KDpg_item_quick_chg.html" >
                    <input type="hidden" name="list_id" value="qualifying_properties" />
                    <input  class="form-control" type="text" name="filter_5" id="first_nameID" onchange="document.getElementById(\'filter_5ID\').submit();" value="' . $_SESSION['qualifying_properties']['filter_5'] . '"/>
                    <input type="button" onclick="document.getElementById(\'first_nameID\').value=\'\';document.getElementById(\'filter_5ID\').submit();" class="btn  btn-default" value="Clear" style="vertical-align: top;"/>
                 </form>
             </div>
             <div class="col-md-4">
                <label class="control-label">Member Id</label>
                <form id="filter_3ID" method="post" action="/a/KDpg_item_quick_chg.html" >
                    <input type="hidden" name="list_id" value="qualifying_properties" />
                    <input  class="form-control" type="text" name="filter_3" id="member_idID" onchange="document.getElementById(\'filter_3ID\').submit();" value="' . $_SESSION['qualifying_properties']['filter_3'] . '"/>
                    <input type="button" onclick="document.getElementById(\'member_idID\').value=\'\';document.getElementById(\'filter_3ID\').submit();" class="btn  btn-default" value="Clear" style="vertical-align: top;"/>
                </form>
             </div>
         </div>
    </div>
    ';



    $html .= '</tr></table></div>
    
    
    <!--filters end -->';

    //-----
    return $html;
}

/**
 * @return string
 */
function getThresholdLookupHTML(){
    //ILHM-1514 2024-10-30 10:36:34  bb
    return '
    <form id="threshold_lookup_tool"  class="form-horizontal">
        <div class="row">
            <div id="threshold_lookup_country" class="col-sm-4">
                <div class="form-group mb-3">
                    <label for="country_lookup">Country</label>
                    ' .renderCountrySelectForLookupTool(). '
                </div>
            </div>
            <div id="threshold_lookup_state" class="col-sm-4"></div>
            <div id="threshold_lookup_county" class="col-sm-4"></div>
        </div>
        <div class="row">
            <div id="threshold_lookup_result" class="col-sm-12 text-center">
                <p style="font-size: 2em; font-weight: bold;"></p>
            </div>
        </div>
    </form>
';
}


/**
 * @return string
 */
function renderCountrySelectForLookupTool(): string {
    //ILHM-1514 2024-10-30 10:36:34  bb
    $countries = array("United States" => "United States", "Canada"=>"Canada", "Other"=>"Other");
    $html = '
        <select  id="country_lookup"  style="width: 100%;" class="form-control styled-select js-example-responsive" >
          <option value="">Select a Country</option>
    ';
    
    foreach ($countries as $key => $option_text) {
        $html .= '<option value="'.$key.'" >'.$option_text.'</option>';
    }
    
    $html .= '</select>';
    return $html;
}

/**
 * @return string
 * ILHM-1614
 */
function renderCountrySelectForLookupTool_2025(): string {
    KDLog("renderCountrySelectForLookupTool_2025");
    $countries = array("United States" => "United States", "Canada"=>"Canada", "Other"=>"Other");
    $html = '
        <select  id="country_lookup" class="form-select rounded-0 border-primary styled-select js-example-responsive" >
          <option value="">Select a Country</option>
    ';
    
    foreach ($countries as $key => $option_text) {
        $html .= '<option value="'.$key.'" >'.$option_text.'</option>';
    }
    
    $html .= '</select>';
    return $html;
}


/**
 * @return string
 * ILHM-1614
 */
function getThresholdLookupHTML_2025(){
    KDLog("getThresholdLookupHTML_2025");

    return '
    <form id="threshold_lookup_tool"  class="form-lookup-tool">
        <div class="row">
            <div id="threshold_lookup_country" class="col-12 col-sm-4 d-flex flex-column align-items-stretch">
                <div class="mb-4 mb-sm-0">
                    <label for="country_lookup" class="form-label">Country</label>
                    ' .renderCountrySelectForLookupTool_2025(). '
                </div>
            </div>
            <div id="threshold_lookup_state" class="col-12 col-sm-4 d-flex flex-column align-items-stretch"></div>
            <div id="threshold_lookup_county" class="col-12 col-sm-4 d-flex flex-column align-items-stretch"></div>
        </div>
        <div id="threshold_lookup_result" class="mt-5 text-center fs-32 fw-bold">
            <p class="m-0"></p>
        </div>
        <div class="px-5 pt-5">
            <h4>Do You Qualify?</h4>
            <p>(You must have three transactions at the price point listed above in the last 24 months.)</p>
            <div class="mb-3 ">
                <div class="form-check form-check-inline text-uppercase">
                    <input class="form-check-input" type="radio" name="qualify" id="qualify_yes" value="Yes">
                    <label class="form-check-label" for="qualify_yes">Yes</label>
                </div>
                <div class="form-check form-check-inline text-uppercase">
                    <input class="form-check-input" type="radio" name="qualify" id="qualify_no" value="No">
                    <label class="form-check-label" for="qualify_no">No</label>
                </div>
            </div>
            <p><strong>Contrats on your achievements!</strong> You may be eligible to take our expedited training. Just 
            submit your application and once approved, you can sign up for Advanced Online, our 4-hour training path which 
            grants you a year of Membership, along with your CLHMS™ Designation.</p>
            <p class="mt-5 text-center"><button class="btn btn-primary">Apply Now</button></p>
        </div>
    </form>
';
}

/**
 * @param $obj
 * @param $i
 * @param $selected_country
 * @param $state_saved
 * @param $selected_state
 * @param $is_cbgl_registered
 * @param string $call_from
 * @return string
 * ILHM-1614
 */
function get_state_html_2025($obj
    , $i
    , $selected_country
    , $state_saved
    , $selected_state
    , $is_cbgl_registered
    , $call_from=''
    , $enableTransactionForm = true): string
{
    $obj->log_message("get_state_html_2025");
    $condition = '';
    $select_a = '';

    if ($selected_country == "United States" ) {
        $condition = ' AND country="US" ';
        $select_a  = 'Select a State';
    } else if ($selected_country == "Canada") {
        $condition = ' AND country="CA" AND item_name !="__"';
        $select_a  = 'Select a Province';
    } else  { //($selected_country == "Other" || !$selected_country )

    }
    $obj->log_message("$selected_country");
    $show_state_alert = 'display:none;';
    $state_or_province = '';
    if ($selected_country == "United States"  ||  $selected_country == "Canada" ) {
        $state_display = ' ';
        if ($call_from == 'country') {            //saved state in the selected country
            $country_by_selected_country = 'Other';
            if ($selected_country == "United States" ) {
                $country_by_selected_country = 'US';
                $state_or_province = 'state';
            } else if ($selected_country == "Canada" ) {
                $country_by_selected_country = 'CA';
                $state_or_province = 'province';
            }
            $country_by_saved_state =  $obj->getFieldContents("States", "item_name", $state_saved, 'country');
            if ($country_by_saved_state == $country_by_selected_country) {
                $show_state_alert = 'display:none;';
            } else {
                $show_state_alert = 'display:block;';
            }
            if (!$selected_state && $state_saved) {
                $show_state_alert = 'display:block;';
            }
        }
    } else {
        $state_display = ' style="display:none;" ';
    }
    //ILHM-0905 2021-10-22 10:19:20AM bb
    $additional_class ='custom-select state transaction_' . $i ;
    if($call_from=='lookup-tool'){
        $additional_class .=' form-control styled-select js-example-responsive';
    }
    //ILHM-1022 [FX] 2022-03-02
    $disabled =  $enableTransactionForm ? '' : ' disabled ' ;//'.$disabled.'
    $state = tablePop('state',$state_saved,'States',//$C_qualifying_properties_row['state']
        array(
            'TP_display_field'		=>'full_name',
            'TP_value_field'		=>'item_name',
            'TP_where'				=>'WHERE item_active=1 '. $condition . ' ', // use 'WHERE 1' to see them all
            'TP_order_by'			=>'list_sort',
            'TP_select_attr'		=>'id="state_'.$i.'"  class="'.$additional_class .'" '.$disabled.'',
            'TP_addOption1'			=>array($select_a,'',false),
            'TP_addOption3'			=>array('Other Territory','OT',true)
        ));
    $html = '
                        <div class="mb-4 mb-sm-0" '.$state_display.'>
                            <label for="state_' . $i . '" class="form-label">State/Prov</label>
    ';

    $html .= $state;
    $html .= '
                            <span style="'.$show_state_alert.'color:red" class="state_alert alert_' . $i . '" id="state_alert_' . $i . '">Please select a '.$state_or_province.'.</span>
                        </div>
    ';
    return $html;
}


/**
 * @param $obj
 * @param $first_option
 * @param $selected_state
 * @param $saved_county
 * @param $is_selected_county
 * @param $call_from
 * @param $saved_state
 * @return array
 * ILHM-1614
 */
function get_county_options_html_helper_2025($obj
    , $first_option
    , $selected_state
    , $saved_county
    , $is_selected_county
    , $call_from
    , $saved_state): array {

    $obj->log_message("get_county_options_html_helper_2025");

    $state_condition = $selected_state;
    if ($call_from == 'country') {
        $state_condition = $saved_state;
    }

    $county_html = $first_option;
    $county_sql = 'SELECT * FROM C_counties WHERE item_active=1 AND state = "'.$state_condition.'"';//'.$state_condition;
    $result = $obj->query($county_sql);

    $has_results = "N";
    while($row = $obj->fetch_assoc($result)) {
        $has_results = "Y";
        $selected = '';
        if ($saved_county == $row['item_name']) {
            $selected = ' selected ';
            $is_selected_county = true;
        }
        $county_html .= '<option value="' . $row['item_name'] . '" ' . $selected . '>' . $row['item_name'] . '</option>';
    }

    if ($has_results == "N") {
        $county_html = '<option value="N/A" selected="selected">N/A</option>';
    }

    return array('county_html' => $county_html, 'is_selected_county' => $is_selected_county);
}


/**
 * @param $obj
 * @param $i
 * @param $is_cbgl_registered
 * @param $selected_values
 * @param $current_property_row
 * @param $call_from
 * @return string
 * ILHM-1614
 */
function get_county_html_2025($obj
    , $i
    , $is_cbgl_registered
    , $selected_values
    , $current_property_row
    , $call_from
    , $enableTransactionForm = true): string {

    $obj->log_message("get_county_html_2025");
    $selected_country = $selected_values['country'];
    $selected_state   = $selected_values['state'];
    $selected_county  = $selected_values['county'];

    //$obj->log_message("selected_values" . print_r($selected_values, true));

    $show_county_alert = 'display:none;';
    $show_county       = 'display:block;';
    $show_county_div   = '';
    //$obj->log_message('FX DEBUG $selected_country: '.print_r($selected_country,true));
    if ($selected_country == "Other" || $selected_country == "") {
        $show_county_div = ' style="display: none;" ';
    } else {

    }
    $HTML = '
                        <div class="form-group" '.$show_county_div.'>
    ';
    //ILHM-0905 2021-10-22 10:19:20AM bb
    $additional_class='custom-select county transaction_' . $i ;
    if($call_from=='lookup-tool'){
        $additional_class.=' form-control styled-select js-example-responsive';
        //$additional_class ='form-control styled-select js-example-responsive select2-hidden-accessible';
    }
    //ILHM-1022 [FX] 2022-03-02
    $disabled =  $enableTransactionForm ? '' : ' disabled ' ;//'.$disabled.'
    $county_html = '<select name="county" id="county_'.$i.'" class="'.$additional_class.'" style="'.$show_county.'" '.$disabled.'>';
    $first_option =   '<option value="">Select a ' .(($selected_country == 'United States')? 'County' : 'Market Area'). '</option>';
    $NA_option =   '<option value="N/A" selected="selected">N/A</option>';
    $is_selected_county = false;
    //   $test_0746 = '0';
    if ($call_from == 'initial_load' || $call_from == 'country') {
        if ($current_property_row['country'] == "United States" || $current_property_row['country'] == "Canada") {
            if ($current_property_row['state'] && $current_property_row['county']) {// && state in the selected country
                $country_by_selected_country = 'Other';
                if ($selected_values['country'] == "United States" ) {

                    $country_by_selected_country = 'US';
                } else if ($selected_values['country'] == "Canada" ) {

                    $country_by_selected_country = 'CA';
                }

                $country_by_saved_state = $obj->getFieldContents("States", "item_name", $current_property_row['state'], 'country');
                if ($country_by_saved_state == $country_by_selected_country ) {//saved state in the selected country
                    $helper_results     = get_county_options_html_helper_2025($obj, $first_option, $selected_state, $current_property_row['county'], $is_selected_county,$call_from,$current_property_row['state']);
                    $is_selected_county = $helper_results['is_selected_county'];
                    $county_html       .= $helper_results['county_html'];

                } else {
                    $first_option =   $NA_option;
                    $county_html .= $first_option;
                    $show_county_alert = 'display:block;';

                }
            }

        } else if ($current_property_row['country'] != '') {
            $first_option =   $NA_option;
            $county_html .= $first_option;
            $show_county_alert = 'display:block;';

        } else {
            if ($call_from == 'country') {
                $show_county_alert = 'display:block;';
            }else {
                $show_county_alert = 'display:none;';
            }
            $first_option =   $NA_option;
            $county_html .= $first_option;

        }
    } else if ($call_from == 'state' || $call_from == 'lookup-tool') { // state_on_change
        $helper_results     = get_county_options_html_V2_helper($obj, $first_option, $selected_state, $current_property_row['county'], $is_selected_county,$call_from,$current_property_row['state']);
        $is_selected_county = $helper_results['is_selected_county'];
        $county_html       .= $helper_results['county_html'];

    }

    if (($is_selected_county || ($selected_country == "United States" && $is_cbgl_registered == "Y")) && $call_from != 'country'){
        $show_county_alert = 'display:none;';
        if ((!$selected_county ||  $selected_county = 'N/A') && $call_from != 'initial_load') {
            $show_county_alert = ' display: block; ';
        }
    } else {
        $show_county_alert = 'display:block;';
    }


    $county_html .= '</select>';
    //ILHM-0746 [FX] 2021-05-26
    if(strpos($county_html, $NA_option) !== false){
        $show_county_alert = 'display:none;';
    }


    if($call_from=='lookup-tool'){
        $show_county_alert = 'display:none;';
    }

    $HTML .= '
                            <label for="county_' . $i . '"  id="county_label_' . $i . '" class="form-label">County/Market Area</label>
                            '.$county_html. '
                            <span style="'.$show_county_alert.'color:red" class="county_alert alert_' . $i . '" id="county_alert_' . $i . '">Please select a county.</span>
                        </div>
    ';


    return $HTML;
}


/**
 * @param $obj
 * @param $selected_values
 * @param $property_type
 * @return int
 * ILHM-1614
 */
function get_county_threshold_2025($obj, $selected_values, $property_type) : int
{
    $obj->log_message("get_county_threshold_2025");
    if ($property_type == 'guild') {
        return  1000000;
    }elseif($property_type == 'elite'){
        return 2000000;
    }elseif($property_type == 'adv_elite'){
        return 3000000;
    }

    $obj->log_message("debugThreshold $property_type " . print_r($selected_values, true));
    if ($selected_values['threshold_at_submit'] > 0) {

        return $selected_values['threshold_at_submit'];
    } elseif ($selected_values['item_id'] > 0 && !$selected_values['lid']) {
        //if we have an item_id but NOT a lid, we didn't pass the whole row but can fetch it
        $current_property_row = $obj->getOneRowV2('C_qualifying_properties', 'item_id', $selected_values['item_id']);
        $obj->log_message("debugThreshold " . print_r($current_property_row, true));
        if($current_property_row['threshold_at_submit'] >0){
            return $current_property_row['threshold_at_submit'];
        }
    }

    $selected_country = $selected_values['country'];
    $selected_state = $selected_values['state'];
    $selected_county = $selected_values['county'];

    $threshold = 500000;
    if ($selected_state && $selected_state != '__' && $selected_county) {
        $sql = 'SELECT * FROM C_counties WHERE item_name="' . $selected_county . '" AND state="' . $selected_state . '"';
        $obj->log_message(" county threshold sql {$sql} " . print_r($selected_values, true));
        $C_counties_row = $obj->getOneRowSQL($sql);
        $threshold = $C_counties_row['threshold'] ? (int) $C_counties_row['threshold'] : 500000;
    }

    if($property_type=='real' && (strtolower($selected_country)=='ca' || strtolower($selected_country)=='canada' )  && $threshold < 1000000){
        return  1000000;
    }elseif($property_type=='real' && $threshold < 750000){
        return 750000;
    }
    if ($selected_country == "Other") {
        return 500000;
    }

    if ($selected_state == 'OT' || $selected_state == 'NT' || $selected_state == 'NU' || $selected_state == 'SK' || $selected_state == 'YT') {
        $threshold = 500000;
    }
    return $threshold;
}

