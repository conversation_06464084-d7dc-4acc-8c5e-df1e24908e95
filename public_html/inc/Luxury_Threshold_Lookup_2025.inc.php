<?php
/**
 * Copyright © 2021 Your Company, Inc.
 *
 * This script will run whenever the page Luxury_Threshold_Lookup.html is accessed.
 *
 * To modify the page fields use $this->db_page_row like this:
 * $this->db_page_row['body'] .= " Hey this is in the body field now!<br />";
 *
 * To replace a token in the page, use curly braces in the page body(or whatever) like this:
 * In the page: ...<input type="text" name="first_name" value="{first_name}">...
 * In this script: $this->db_page_row['body'] = str_replace('{first_name}',$first_name,$this->db_page_row['body']);
*/




//ILHM-1327 2024-02-13 12:52:40  bb
$str_start = strpos($this->db_page_row['body'], '{price_');
if($str_start >0){
    //we need to sub a price for a particular SKU
    $str_end = strpos($this->db_page_row['body'], '}',$str_start);
    $token = substr($this->db_page_row['body'], $str_start,($str_end - $str_start));
    $sku = str_replace('{price_', '', $token);
    $product_row =  $this->getOneRowV2('Products','item_name',$sku);
    //dd($token);
    $this->db_page_row['body'] = str_replace($token."}",trim($product_row['member_prices']), $this->db_page_row['body']);
    
}

//// ILHM-0905 2021-10-28 JB - add lookup tool
//ILHM-1514 2024-10-30 10:26:16  refactored to use a function in cust_functions_applecations.inc.php
require_once __DIR__ . '/cust_functions_applications.inc.php';
$this->db_page_row['body'] = str_replace('{look_up_tool}', getThresholdLookupHTML_2025(), $this->db_page_row['body']);

if(str_contains($this->db_page_row['body'], '{showcase_member_html}')){
    //ILHM-1614 2025-07-16 06:09:08  bb we want a random clhms member here
    $sql ="SELECT * FROM Members WHERE 1
           AND item_active=1
            AND designation='C'
            AND attachment_filename !=''
            ORDER BY RAND() LIMIT 1";
    //dd($sql);
    $showcase_member_row = $this->getOneRowSQL($sql);
    
    $showcase_html ='
            <div class="image image-designation position-relative">
                <img src="https://www.luxuryhomemarketing.com/member_images/'. $showcase_member_row['attachment_filename'] .'" class="img-fluid w-100" alt="Showcase Member: '. $showcase_member_row['first_name'] .' '. $showcase_member_row['last_name'] .'">
                <div class="image-designation-overlay position-absolute bottom-0 start-0 d-flex gap-3 justify-content-between align-items-center w-100 px-3 py-2 bg-black" style="--bs-bg-opacity: .7;">
                    <div class="overlay-text text-white fs-14">
                        '. $showcase_member_row['first_name'] .' '. $showcase_member_row['last_name'] .'<br>
                        <em>Institute Member CLHMS&trade;</em><br>
                        <em>'. $showcase_member_row['office'] .' </em>
                    </div>
                    <div class="overlay-badge" style="width: 60px;">
                        <img src="/Logos/ILHM_CLHMS_Seal_RGB_thumbnail_125_1187628351_9558.png" alt="CLHMS Seal" class="img-fluid">
                    </div>
                </div>
            </div>
';
    $this->db_page_row['body'] = str_replace('{showcase_member_html}', $showcase_html, $this->db_page_row['body']);
}





function renderCountrySelect(): string {
    $countries = array("United States" => "United States", "Canada"=>"Canada", "Other"=>"Other");
    $html = '
        <select  id="country_1" class="form-select rounded-0 border-primary styled-select js-example-responsive" >
          <option value="">Select a Country</option>
    ';

    foreach ($countries as $key => $option_text) {
        $html .= '<option value="'.$key.'" >'.$option_text.'</option>';
    }

    $html .= '</select>';
    return $html;
}

// 2024-01-29 cb add the similar lookup tool for CBGL that uses zip instead of county
 require_once __DIR__ . '/CBGL_Threshold.inc.php';

